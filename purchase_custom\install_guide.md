# Guide d'Installation - Purchase Custom

## Prérequis

- Odoo 17.0 installé et fonctionnel
- Module `purchase_requisition` installé
- Accès administrateur à l'instance Odoo

## Étapes d'Installation

### 1. Copier le module

Copier le dossier `purchase_custom` dans le répertoire addons d'Odoo :

```bash
cp -r purchase_custom /path/to/odoo/addons/
```

### 2. Redémarrer Odoo

Redémarrer le service Odoo pour qu'il détecte le nouveau module :

```bash
sudo systemctl restart odoo
# ou
sudo service odoo restart
```

### 3. Mettre à jour la liste des modules

1. Se connecter à Odoo en tant qu'administrateur
2. Aller dans **Applications**
3. Cliquer sur **Mettre à jour la liste des applications**

### 4. Installer le module

1. Dans **Applications**, rechercher "Purchase Custom"
2. Cliquer sur **Installer**

### 5. Vérification de l'installation

1. Aller dans **Achats > Configuration > Conventions d'achat**
2. Créer une nouvelle convention d'achat
3. Vérifier que :
   - Le champ "Sociétés" est visible et éditable
   - Le champ est pré-rempli avec la société courante
   - On peut sélectionner plusieurs sociétés
   - Les produits sont filtrés selon les sociétés sélectionnées

## Tests

Pour exécuter les tests du module :

```bash
odoo-bin -d your_database -i purchase_custom --test-enable --stop-after-init
```

## Dépannage

### Erreur lors de l'installation

Si le module ne s'installe pas :

1. Vérifier les logs Odoo : `/var/log/odoo/odoo.log`
2. S'assurer que le module `purchase_requisition` est installé
3. Vérifier les permissions sur les fichiers du module

### Champ "Sociétés" non visible

Si le champ "Sociétés" n'apparaît pas :

1. Vérifier que l'utilisateur a les droits multi-sociétés
2. Aller dans **Paramètres > Utilisateurs & Sociétés > Sociétés**
3. S'assurer qu'il y a plusieurs sociétés configurées

### Problèmes de domaine produit

Si les produits ne sont pas filtrés correctement :

1. Vérifier que les produits ont bien une société assignée
2. Aller dans **Inventaire > Configuration > Produits**
3. Éditer un produit et vérifier le champ "Société"

## Migration depuis l'ancien système

Si vous migrez depuis un système utilisant `company_id` :

1. **Sauvegarde** : Faire une sauvegarde complète de la base de données
2. **Migration des données** : Exécuter le script de migration (si fourni)
3. **Test** : Tester sur un environnement de développement d'abord

## Support

Pour toute question ou problème :

1. Consulter les logs Odoo
2. Vérifier la documentation du module
3. Contacter l'équipe de développement

## Désinstallation

Pour désinstaller le module :

1. Aller dans **Applications**
2. Rechercher "Purchase Custom"
3. Cliquer sur **Désinstaller**

**Attention** : La désinstallation supprimera les données du champ `company_ids`. Faire une sauvegarde avant de désinstaller.
