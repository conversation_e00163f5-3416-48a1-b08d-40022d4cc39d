# -*- coding: utf-8 -*-
{
    'name': 'Purchase Custom - Multi-Company Requisitions',
    'version': '********.0',
    'category': 'Purchase',
    'summary': 'Customize purchase requisitions to support multiple companies',
    'description': """
Purchase Custom - Multi-Company Requisitions
============================================

This module customizes the purchase requisition module to support multiple companies.
It allows purchase requisitions to be used across two companies (Jean <PERSON> & LEH).

Key Features:
- Replace single company_id with multiple company_ids in purchase.requisition
- Automatic initialization of company field with current user context
- Editable company field for manual modification
- Updated security rules and views to support multi-company functionality

Technical Changes:
- Modified purchase.requisition model to use company_ids (many2many) instead of company_id
- Updated XML views to hide company_id and use company_ids in product domains
- Archived existing security rules for multi-company support
- Redefined onchange methods to work with multiple companies
    """,
    'author': 'Your Company',
    'website': 'https://www.yourcompany.com',
    'depends': ['purchase_requisition'],
    'data': [
        'security/purchase_requisition_security.xml',
        'views/purchase_requisition_views.xml',
    ],
    'test': [
        'tests/test_purchase_requisition_multi_company.py',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
}
