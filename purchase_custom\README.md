# Purchase Custom - Multi-Company Requisitions

## Description

Ce module personnalise le module purchase_requisition d'Odoo pour permettre l'utilisation des contrats d'achat sur plusieurs entreprises (Jean Hervé & LEH).

## Fonctionnalités

### Modifications principales

1. **Champ Société Multiple** : Remplacement du champ `company_id` (Many2one) par `company_ids` (Many2many) dans le modèle `purchase.requisition`
2. **Initialisation Automatique** : Le champ société est automatiquement initialisé avec la société sélectionnée par l'utilisateur (contexte actif)
3. **Édition Manuelle** : L'utilisateur peut modifier ce champ manuellement si nécessaire
4. **Domaines Mis à Jour** : Les domaines des produits utilisent maintenant `company_ids` au lieu de `company_id`

### Modifications techniques

#### Modèle purchase.requisition
- `company_id` : `required=False`, `default=False`, caché dans les vues XML
- `company_ids` : nouveau champ Many2many vers `res.company`, `required=True`, `default=lambda self: self.env.company`, `tracking=True`

#### Règles de sécurité
- Archivage des règles `purchase_requisition_comp_rule` et `purchase_requisition_line_comp_rule`

#### Vues XML
- Champ `company_id` caché
- Nouveau champ `company_ids` avec widget `many2many_tags`
- Domaines des produits mis à jour pour utiliser `company_ids`

#### Méthodes redéfinies

##### purchase.requisition._onchange_vendor()
```python
@api.onchange('vendor_id')
def _onchange_vendor(self):
    """Redefine this method because we no longer use `company_id` in purchase.requisition; it has been replaced by `company_ids`."""
    requisitions = self.env['purchase.requisition'].search([
        ('vendor_id', '=', self.vendor_id.id),
        ('state', '=', 'confirmed'),
        ('requisition_type', '=', 'blanket_order'),
        ('company_ids', 'in', self.company_ids.ids),  # Utilise company_ids au lieu de company_id
    ])
```

##### purchase.order._onchange_requisition_id()
```python
@api.onchange('requisition_id')
def _onchange_requisition_id(self):
    """Redefine this method because we no longer use `company_id` in purchase.requisition; it has been replaced by `company_ids`."""
    # Suppression de la ligne : self.company_id = requisition.company_id.id
    # Correction des taxes : utilise self.company_id au lieu de requisition.company_id
    taxes_ids = fpos.map_tax(line.product_id.supplier_taxes_id.filtered(lambda tax: tax.company_id == self.company_id)).ids
```

## Installation

1. Placer le module dans le répertoire addons d'Odoo
2. Mettre à jour la liste des modules
3. Installer le module `purchase_custom`

## Dépendances

- `purchase_requisition`

## Utilisation

1. Créer une nouvelle convention d'achat
2. Le champ "Sociétés" sera automatiquement rempli avec la société courante
3. Modifier manuellement les sociétés si nécessaire
4. Les produits disponibles seront filtrés selon les sociétés sélectionnées

## Compatibilité

- Odoo 17.0
- Module purchase_requisition standard

## Support

Pour toute question ou problème, veuillez contacter l'équipe de développement.
