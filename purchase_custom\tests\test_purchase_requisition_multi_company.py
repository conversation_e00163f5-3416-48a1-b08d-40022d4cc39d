# -*- coding: utf-8 -*-

from odoo.tests.common import TransactionCase
from odoo.exceptions import ValidationError


class TestPurchaseRequisitionMultiCompany(TransactionCase):

    def setUp(self):
        super().setUp()
        
        # Create test companies
        self.company_jean_herve = self.env['res.company'].create({
            'name': '<PERSON>',
            'currency_id': self.env.ref('base.EUR').id,
        })
        
        self.company_leh = self.env['res.company'].create({
            'name': 'LEH',
            'currency_id': self.env.ref('base.EUR').id,
        })
        
        # Create test vendor
        self.vendor = self.env['res.partner'].create({
            'name': 'Test Vendor',
            'is_company': True,
            'supplier_rank': 1,
        })
        
        # Create test product
        self.product = self.env['product.product'].create({
            'name': 'Test Product',
            'purchase_ok': True,
            'type': 'product',
        })

    def test_purchase_requisition_company_ids_default(self):
        """Test that company_ids is properly set by default"""
        requisition = self.env['purchase.requisition'].create({
            'vendor_id': self.vendor.id,
            'requisition_type': 'blanket_order',
        })
        
        # Should have current company by default
        self.assertTrue(requisition.company_ids)
        self.assertIn(self.env.company, requisition.company_ids)

    def test_purchase_requisition_multiple_companies(self):
        """Test that we can set multiple companies"""
        requisition = self.env['purchase.requisition'].create({
            'vendor_id': self.vendor.id,
            'requisition_type': 'blanket_order',
            'company_ids': [(6, 0, [self.company_jean_herve.id, self.company_leh.id])],
        })
        
        # Should have both companies
        self.assertEqual(len(requisition.company_ids), 2)
        self.assertIn(self.company_jean_herve, requisition.company_ids)
        self.assertIn(self.company_leh, requisition.company_ids)

    def test_purchase_requisition_company_id_not_required(self):
        """Test that company_id is not required anymore"""
        requisition = self.env['purchase.requisition'].create({
            'vendor_id': self.vendor.id,
            'requisition_type': 'blanket_order',
            'company_ids': [(6, 0, [self.company_jean_herve.id])],
            'company_id': False,  # Should be allowed
        })
        
        self.assertFalse(requisition.company_id)
        self.assertTrue(requisition.company_ids)

    def test_purchase_requisition_company_ids_required(self):
        """Test that company_ids is required"""
        with self.assertRaises(ValidationError):
            self.env['purchase.requisition'].create({
                'vendor_id': self.vendor.id,
                'requisition_type': 'blanket_order',
                'company_ids': [(5, 0, 0)],  # Clear all companies - should fail
            })

    def test_onchange_vendor_with_company_ids(self):
        """Test that _onchange_vendor works with company_ids"""
        # Create first requisition
        requisition1 = self.env['purchase.requisition'].create({
            'vendor_id': self.vendor.id,
            'requisition_type': 'blanket_order',
            'state': 'confirmed',
            'company_ids': [(6, 0, [self.company_jean_herve.id])],
        })
        
        # Create second requisition with same vendor and overlapping companies
        requisition2 = self.env['purchase.requisition'].new({
            'vendor_id': self.vendor.id,
            'requisition_type': 'blanket_order',
            'company_ids': [(6, 0, [self.company_jean_herve.id, self.company_leh.id])],
        })
        
        # Should trigger warning
        result = requisition2._onchange_vendor()
        self.assertIsNotNone(result)
        self.assertIn('warning', result)

    def test_purchase_order_onchange_requisition_id(self):
        """Test that purchase order onchange works without company_id from requisition"""
        requisition = self.env['purchase.requisition'].create({
            'vendor_id': self.vendor.id,
            'requisition_type': 'purchase_template',
            'company_ids': [(6, 0, [self.company_jean_herve.id])],
            'line_ids': [(0, 0, {
                'product_id': self.product.id,
                'product_qty': 10,
                'price_unit': 100,
            })],
        })
        
        # Create purchase order
        purchase_order = self.env['purchase.order'].with_company(self.company_jean_herve).create({
            'partner_id': self.vendor.id,
            'company_id': self.company_jean_herve.id,
        })
        
        # Set requisition and trigger onchange
        purchase_order.requisition_id = requisition
        purchase_order._onchange_requisition_id()
        
        # Should have order lines
        self.assertTrue(purchase_order.order_line)
        # Company should remain the same (not changed by requisition)
        self.assertEqual(purchase_order.company_id, self.company_jean_herve)
