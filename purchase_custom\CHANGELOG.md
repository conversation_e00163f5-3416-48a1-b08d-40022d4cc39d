# Changelog - Purchase Custom

## Version 17.0.1.0.0 - Initial Release

### Ajouts

#### Nouveaux champs
- **purchase.requisition.company_ids** : Champ Many2many vers res.company
  - Remplace l'utilisation de company_id pour permettre plusieurs sociétés
  - Requis : True
  - Défaut : Société courante de l'utilisateur
  - Tracking : True

#### Nouvelles vues
- **Vue formulaire purchase.requisition** : 
  - Champ company_id caché
  - Nouveau champ company_ids avec widget many2many_tags
  - Domaines produits mis à jour pour utiliser company_ids

- **Vue liste purchase.requisition** :
  - Champ company_id caché
  - Nouveau champ company_ids visible en option

#### Tests
- **TestPurchaseRequisitionMultiCompany** : Suite de tests complète
  - Test de la valeur par défaut de company_ids
  - Test de la sélection multiple de sociétés
  - Test que company_id n'est plus requis
  - Test que company_ids est requis
  - Test de la méthode _onchange_vendor avec company_ids
  - Test de la méthode _onchange_requisition_id

### Modifications

#### Modèles

##### purchase.requisition
- **company_id** : 
  - `required=False` (était True)
  - `default=False` (était lambda self: self.env.company)
  - Caché dans les vues XML

- **_onchange_vendor()** : Redéfinie pour utiliser company_ids
  ```python
  # Avant
  ('company_id', '=', self.company_id.id)
  
  # Après  
  ('company_ids', 'in', self.company_ids.ids)
  ```

##### purchase.order
- **_onchange_requisition_id()** : Redéfinie pour supprimer la dépendance à requisition.company_id
  ```python
  # Ligne supprimée
  # self.company_id = requisition.company_id.id
  
  # Ligne modifiée pour les taxes
  # Avant
  taxes_ids = fpos.map_tax(line.product_id.supplier_taxes_id.filtered(lambda tax: tax.company_id == requisition.company_id)).ids
  
  # Après
  taxes_ids = fpos.map_tax(line.product_id.supplier_taxes_id.filtered(lambda tax: tax.company_id == self.company_id)).ids
  ```

#### Sécurité
- **purchase_requisition_comp_rule** : Archivée (active=False)
- **purchase_requisition_line_comp_rule** : Archivée (active=False)

#### Vues XML
- **Domaines produits** : Mis à jour pour utiliser company_ids
  ```xml
  <!-- Avant -->
  domain="[('purchase_ok', '=', True), '|', ('company_id', '=', False), ('company_id', '=', parent.company_id)]"
  
  <!-- Après -->
  domain="[('purchase_ok', '=', True), '|', ('company_id', '=', False), ('company_id', 'in', parent.company_ids)]"
  ```

### Corrections

#### Problèmes résolus
1. **Limitation mono-société** : Les conventions d'achat peuvent maintenant être utilisées sur plusieurs sociétés
2. **Filtrage produits** : Les produits sont correctement filtrés selon les sociétés sélectionnées
3. **Taxes incorrectes** : Les taxes sont maintenant calculées avec la bonne société dans purchase.order
4. **Règles de sécurité** : Les anciennes règles basées sur company_id sont désactivées

### Impact sur les données existantes

#### Migration nécessaire
- Les conventions d'achat existantes auront company_id=False et company_ids vide
- Il faudra migrer les données pour remplir company_ids avec les valeurs de company_id

#### Script de migration suggéré
```python
# À exécuter après installation du module
requisitions = env['purchase.requisition'].search([('company_ids', '=', False)])
for req in requisitions:
    if req.company_id:
        req.company_ids = [(6, 0, [req.company_id.id])]
    else:
        req.company_ids = [(6, 0, [env.company.id])]
```

### Compatibilité

#### Versions supportées
- Odoo 17.0

#### Modules requis
- purchase_requisition (standard Odoo)

#### Modules optionnels
- Aucun

### Notes techniques

#### Performance
- L'utilisation de Many2many peut avoir un impact sur les performances pour de gros volumes
- Les domaines avec 'in' sont généralement plus lents que '='

#### Sécurité
- Les règles de sécurité existantes sont désactivées
- Il peut être nécessaire de créer de nouvelles règles basées sur company_ids

#### Extensibilité
- Le module est conçu pour être facilement extensible
- D'autres modules peuvent hériter des modifications
