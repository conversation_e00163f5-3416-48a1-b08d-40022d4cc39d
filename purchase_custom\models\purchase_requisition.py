# -*- coding: utf-8 -*-

from odoo import api, fields, models, _


class PurchaseRequisition(models.Model):
    _inherit = 'purchase.requisition'

    # Modify existing company_id field
    company_id = fields.Many2one(
        'res.company', 
        string='Company', 
        required=False, 
        default=False
    )
    
    # Add new company_ids field
    company_ids = fields.Many2many(
        'res.company',
        string='Companies',
        required=True,
        default=lambda self: self.env.company,
        tracking=True,
        help="Companies for which this purchase requisition applies"
    )

    @api.onchange('vendor_id')
    def _onchange_vendor(self):
        """Redefine this method because we no longer use `company_id` in purchase.requisition; it has been replaced by `company_ids`."""
        requisitions = self.env['purchase.requisition'].search([
            ('vendor_id', '=', self.vendor_id.id),
            ('state', '=', 'confirmed'),
            ('requisition_type', '=', 'blanket_order'),
            # Start patch: Replace company_id by company_ids because we no longer use `company_id` in purchase.requisition; it has been replaced by `company_ids`.
            ('company_ids', 'in', self.company_ids.ids),
            # End patch
        ])
        if any(requisitions):
            title = _("Warning for %s", self.vendor_id.name)
            message = _("There is already an open blanket order for this supplier. We suggest you complete this open blanket order, instead of creating a new one.")
            warning = {
                'title': title,
                'message': message
            }
            return {'warning': warning}
