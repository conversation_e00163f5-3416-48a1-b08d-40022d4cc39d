# Translation of Odoo Server.
# This file contains the translation of the following modules:
# * purchase_requisition
#
# Translators:
msgid ""
msgstr ""
"Project-Id-Version: Odoo 9.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2023-05-16 13:49+0000\n"
"PO-Revision-Date: 2016-06-09 10:41+0000\n"
"Last-Translator: <PERSON>\n"
"Language-Team: Lao (http://www.transifex.com/odoo/odoo-9/language/lo/)\n"
"Language: lo\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: purchase_requisition
#: model:ir.actions.report,print_report_name:purchase_requisition.action_report_purchase_requisitions
msgid "'Purchase Agreement - %s' % (object.name)"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Agreement Deadline:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Date</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Description</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Product UoM</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Qty</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Reference </strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Scheduled Date</strong>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Scheduled Ordering Date:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Source:</strong><br/>"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "<strong>Vendor </strong>"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_needaction
msgid "Action Needed"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__active
msgid "Active"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_ids
msgid "Activities"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_state
msgid "Activity State"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_type_icon
msgid "Activity Type Icon"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_product_supplierinfo__purchase_requisition_id
msgid "Agreement"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__date_end
msgid "Agreement Deadline"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__exclusive
msgid "Agreement Selection Type"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__type_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__name
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_form
msgid "Agreement Type"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__alternative_po_ids
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__alternative_po_ids
msgid "Alternative POs"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__alternative_po_count
msgid "Alternative Po Count"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Alternative Purchase Order"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "Alternative Warning"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Alternatives"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid "An example of a purchase agreement is a blanket order."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__analytic_distribution
msgid "Analytic Distribution"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__analytic_distribution_search
msgid "Analytic Distribution Search"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__analytic_precision
msgid "Analytic Precision"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_search
msgid "Archived"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_attachment_count
msgid "Attachment Count"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__open
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state_blanket_order__open
msgid "Bid Selection"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__requisition_id
#: model:purchase.requisition.type,name:purchase_requisition.type_single
msgid "Blanket Order"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition
#: model:ir.ui.menu,name:purchase_requisition.menu_purchase_requisition_pro_mgt
msgid "Blanket Orders"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_create_alternative_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Cancel"
msgstr "ຍົກເລີອກ"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "Cancel Alternatives"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__cancel
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state_blanket_order__cancel
msgid "Cancelled"
msgstr "ຍົກເລີກ"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "Cancelled by the agreement associated to this quotation."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_uom_category_id
msgid "Category"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Choose"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__partner_id
msgid "Choose a vendor for alternative PO"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Clear"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Clear Selected"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Close"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__done
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state_blanket_order__done
msgid "Closed"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__company_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__company_id
msgid "Company"
msgstr "ບໍລິສັດ"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Compare Order Lines"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Compare Product Lines"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Confirm"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__in_progress
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state_blanket_order__in_progress
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Confirmed"
msgstr "ຮັບຮູ້ເເລັວ"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_line__product_uom_category_id
msgid "Conversion between Units of Measure can only occur if they belong to the same category. The conversion will be made based on the ratios."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__copy_products
msgid "Copy Products"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_create_alternative_form
msgid "Create Alternative"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid ""
"Create a call for tender by adding alternative request for quotations to different vendors.\n"
"                            Make your choice by selecting the best combination of lead time, OTD and/or total amount.\n"
"                            By comparing product lines you can also decide to order some products from one vendor and others from another vendor."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Create alternative"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__create_uid
msgid "Created by"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__create_date
msgid "Created on"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__creation_blocked
msgid "Creation Blocked"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__currency_id
msgid "Currency"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_description_variants
msgid "Custom Description"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_form
msgid "Data for new quotations"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__schedule_date
msgid "Delivery Date"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__description
msgid "Description"
msgstr "ຄຳອະທິບາຍ"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__display_name
msgid "Display Name"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition_type__line_copy__none
msgid "Do not create RfQ lines automatically"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Done"
msgstr "ຈົບເເລັວ"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__draft
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state_blanket_order__draft
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Draft"
msgstr "ຕົວທົດລອງ"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_follower_ids
msgid "Followers"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_partner_ids
msgid "Followers (Partners)"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid ""
"For a blanket order, you can record an agreement for a specific period\n"
"            (e.g. a year) and you order products within this agreement to benefit\n"
"            from the negotiated prices."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Future Activities"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Group By"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__has_alternatives
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_kpis_tree_inherit_purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_tree_inherit_purchase_requisition
msgid "Has Alternatives"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__has_message
msgid "Has Message"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__id
msgid "ID"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_exception_icon
msgid "Icon"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_error
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__creation_blocked
msgid "If the chosen vendor or if any of the products in the original PO have a blocking warning then we prevent creation of alternative PO. This is because normally these fields are cleared w/warning message within form view, but we cannot recreate that in this case."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__copy_products
msgid "If this is checked, the product quantities of the original PO will be copied"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "In negotiation"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_is_follower
msgid "Is Follower"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "Keep Alternatives"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__write_uid
msgid "Last Updated by"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__write_date
msgid "Last Updated on"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Late Activities"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__line_copy
msgid "Lines"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Link to Existing RfQ"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_error
msgid "Message Delivery error"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_ids
msgid "Messages"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "My Agreements"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Name, TIN, Email, or Reference"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "New Agreements"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "New Quotation"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_date_deadline
msgid "Next Activity Deadline"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_summary
msgid "Next Activity Summary"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_type_id
msgid "Next Activity Type"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Nothing to clear"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_needaction_counter
msgid "Number of Actions"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__order_count
msgid "Number of Orders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_error_counter
msgid "Number of errors"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_needaction_counter
msgid "Number of messages which requires an action"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__ongoing
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state_blanket_order__ongoing
msgid "Ongoing"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__order_ids
msgid "Order"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__qty_ordered
msgid "Ordered Quantities"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__ordering_date
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Ordering Date"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__origin_po_id
msgid "Origin Po"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_order__alternative_po_ids
msgid "Other potential purchase orders for purchasing products"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__po_ids
msgid "POs to Confirm"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Price Unit"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__product_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_id
msgid "Product"
msgstr "ສິນຄ້າ"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_uom_id
msgid "Product Unit of Measure"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_product
msgid "Product Variant"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Products"
msgstr "ສິນຄ້າ"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__line_ids
msgid "Products to Purchase"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__requisition_id
msgid "Purchase Agreement"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_type_tree
msgid "Purchase Agreement Types"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.report,name:purchase_requisition.action_report_purchase_requisitions
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_tree
msgid "Purchase Agreements"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__purchase_group_id
msgid "Purchase Group"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order
msgid "Purchase Order"
msgstr "​ການ​ສັ່ງ​ຊື້"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "​ການ​ສັ່ງ​ຊື້ທາງ​ອອນ​ລາຍ"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Purchase Order Lines"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__purchase_ids
msgid "Purchase Orders"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Purchase Orders with requisition"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__user_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Purchase Representative"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition
msgid "Purchase Requisition"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_line
#: model:ir.model.fields,field_description:purchase_requisition.field_product_supplierinfo__purchase_requisition_line_id
msgid "Purchase Requisition Line"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_type
msgid "Purchase Requisition Type"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__is_quantity_copy
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__is_quantity_copy
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__quantity_copy
msgid "Quantities"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_qty
msgid "Quantity"
msgstr "ຈຳນວນ"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "RFQs/Orders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__rating_ids
msgid "Ratings"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__name
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Reference"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Reference:"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition_to_so
msgid "Request for Quotation"
msgstr ""

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition_list
msgid "Request for Quotations"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisitions
msgid "Requests for Quotation Details"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Requisition"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Reset to Draft"
msgstr "ປັບກັບຮ່າງ"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_user_id
msgid "Responsible User"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_sms_error
msgid "SMS Delivery error"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__schedule_date
msgid "Scheduled Date"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Search Purchase Agreements"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition_type__exclusive__multiple
msgid "Select multiple RFQ (non-exclusive)"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition_type__exclusive__exclusive
msgid "Select only one RFQ (exclusive)"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_type__exclusive
msgid ""
"Select only one RFQ (exclusive):  when a purchase order is confirmed, cancel the remaining purchase order.\n"
"\n"
"                    Select multiple RFQ (non-exclusive): allows multiple purchase orders. On confirmation of a purchase order it does not cancel the remaining orders"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_type__sequence
msgid "Sequence"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_type__active
msgid "Set active to false to hide the Purchase Agreement Types without removing it."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition_type__quantity_copy__none
msgid "Set quantities manually"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Show all records which has next action date is before today"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Some not cleared"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Some quantities were not cleared because their status is not a RFQ status."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__origin
msgid "Source Document"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid "Start a new purchase agreement"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__state_blanket_order
msgid "State Blanket Order"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__state
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Status"
msgstr "ສະພາບ"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__supplier_info_ids
msgid "Supplier Info"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order_group
msgid "Technical model to group PO for call to tenders"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Terms and Conditions"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__schedule_date
msgid "The expected and scheduled delivery date where all the products are received"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__origin_po_id
msgid "The original PO that this alternative PO is being created for."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid "The vendor you have selected or at least one of the products you are copying from the original order has a blocking warning on it and cannot be selected to create an alternative."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "There are no quantities to clear."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "There is already an open blanket order for this supplier. We suggest you complete this open blanket order, instead of creating a new one."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid "This is a blocking warning!\n"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Today Activities"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Total"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__price_unit
msgid "Unit Price"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "UoM"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition_type__line_copy__copy
msgid "Use lines of agreement"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition_type__quantity_copy__copy
msgid "Use quantities of agreement"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Validate"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__vendor_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__partner_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Vendor"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__purchase_warn_msg
msgid "Warning Messages"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid ""
"Warning for %(partner)s:\n"
"%(warning_message)s\n"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid ""
"Warning for %(product)s:\n"
"%(warning_message)s\n"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "Warning for %s"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__website_message_ids
msgid "Website Messages"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__website_message_ids
msgid "Website communication history"
msgstr ""

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_order__has_alternatives
msgid "Whether or not this purchase order is linked to another purchase order as an alternative."
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_alternative_warning
msgid "Wizard in case PO still has open alternative requests for quotation"
msgstr ""

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_create_alternative
msgid "Wizard to preset values for alternative PO"
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "You can only delete draft or cancelled requisitions."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "You cannot confirm agreement '%s' because there is no product line."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "You cannot confirm the blanket order without price."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "You cannot confirm the blanket order without quantity."
msgstr ""

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "You have to cancel or validate every RfQ before closing the purchase requisition."
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "alternative request for quotations are still open, do you want to cancel them?"
msgstr ""

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "e.g. PO0025"
msgstr ""
