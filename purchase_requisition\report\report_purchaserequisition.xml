<?xml version="1.0" encoding="utf-8"?>
<odoo>
<data>
<template id="report_purchaserequisition_document">
    <t t-set="o" t-value="o.with_context(lang=o.vendor_id.lang)"/>
    <t t-call="web.external_layout">
        <t t-set="address">
            <span t-field="o.vendor_id" t-options='{"widget": "contact", "fields": ["name","address"], "no_marker": True}'/>
            <br/>
            <span t-field="o.vendor_id" t-options='{"widget": "contact", "fields": ["phone"], "phone_icons": True}'/>
            <p t-if="o.vendor_id.vat"><t t-out="o.company_id.account_fiscal_country_id.vat_label or 'Tax ID'"/>: <span t-field="o.vendor_id.vat"/></p>
        </t>
        <div class="page">
            <div class="oe_structure"/>
            <h2><span t-out="dict(o._fields['requisition_type'].selection).get(o.requisition_type)">Purchase Agreement:</span> <span t-field="o.name">BO00004</span></h2>
            <div class="row my-2">
                <div t-if="o.date_end" class="col-3">
                    <strong t-if="o.requisition_type == 'blanket_order'">Agreement Validity:</strong><br/>
                    <span t-if="o.requisition_type == 'blanket_order'" t-field="o.date_end">2023-09-15</span>
                </div>
                <div t-if="o.user_id" class="col-3">
                    <strong>Contact:</strong><br/>
                    <span t-field="o.user_id.name">Mitchell Admin</span>
                </div>
                <div t-if="o.reference" class="col-3">
                    <strong>Reference:</strong><br/>
                    <span t-field="o.reference">Demo Reference</span>
                </div>
            </div>

            <t t-if="o.line_ids">
                <h3>Products</h3>
                <div class="oe_structure"></div>
                <table class="table table-borderless">
                <t t-set="has_ordered_quantity" t-value="any(line.qty_ordered != 0 for line in o.line_ids)"/>
                    <thead>
                        <tr>
                            <th class="text-start">Product</th>
                            <th class="text-end">Quantity</th>
                            <th t-if="has_ordered_quantity" class="text-end">Ordered</th>
                            <th class="text-start" groups="uom.group_uom">Unit</th>
                            <th class="text-end">Unit Price</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr t-foreach="o.line_ids" t-as="line_id">
                            <td>
                                <t t-if="line_id.product_description_variants">
                                    <span t-field="line_id.product_description_variants"/>
                                </t>
                                <t t-else="">
                                    <span t-if="line_id.product_id.code">
                                        [ <span t-field="line_id.product_id.code">Code</span> ]
                                    </span>
                                    <span t-field="line_id.product_id.name">Product</span>
                                </t>
                            </td>
                            <td class="text-end">
                                <span t-field="line_id.product_qty">5</span>
                            </td>
                            <td class="text-end" t-if="has_ordered_quantity">
                                <span t-field="line_id.qty_ordered">5</span>
                            </td>
                            <td class="text-start" groups="uom.group_uom">
                                <span t-field="line_id.product_uom_id.name">Unit</span>
                            </td>
                            <td class="text-end">
                                <span t-field="line_id.price_unit" t-options='{"widget": "monetary", "display_currency": line_id.requisition_id.currency_id}'>$50</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </t>
            <t t-if="o.purchase_ids">
                <h3>Orders</h3>
                <table class="table table-borderless">
                    <thead>
                        <tr>
                            <th class="text-start">Reference</th>
                            <th class="text-start">Date</th>
                            <th class="text-start">Buyer</th>
                            <th class="text-start">Expected on</th>
                            <th class="text-end">Total</th>
                            <th class="text-start">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr t-foreach="o.purchase_ids" t-as="purchase_id">
                            <td>
                                <span t-field="purchase_id.name">Purchase Reference</span>
                            </td>
                            <td>
                                <span t-field="purchase_id.date_order" t-options='{"widget": "date"}'>02/16/2024</span>
                            </td>
                            <td>
                                <span t-if="purchase_id.user_id" t-field="purchase_id.user_id.name">Mitchell Admin</span>
                            </td>
                            <td>
                                <span t-if="purchase_id.date_planned" t-field="purchase_id.date_planned" t-options='{"widget": "date"}'>12/25/2024</span>
                            </td>
                            <td class="text-end">
                                <span t-field="purchase_id.amount_total">$500</span>
                            </td>
                            <td>
                                <span t-field="purchase_id.state">RFQ</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </t>

            <div t-if="o.description" t-out="o.description"/>

            <div class="oe_structure"/>
        </div>
    </t>
</template>
<template id="report_purchaserequisitions">
    <t t-call="web.html_container">
        <t t-foreach="docs" t-as="o">
            <t t-call="purchase_requisition.report_purchaserequisition_document" t-lang="o.vendor_id.lang"/>
        </t>
    </t>
</template>
</data>
</odoo>
