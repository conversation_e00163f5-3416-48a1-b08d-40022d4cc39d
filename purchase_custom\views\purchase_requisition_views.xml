<?xml version="1.0"?>
<odoo>

    <!-- Inherit the purchase requisition form view to hide company_id and add company_ids -->
    <record id="view_purchase_requisition_form_inherit" model="ir.ui.view">
        <field name="name">purchase.requisition.form.inherit.custom</field>
        <field name="model">purchase.requisition</field>
        <field name="inherit_id" ref="purchase_requisition.view_purchase_requisition_form"/>
        <field name="arch" type="xml">
            <!-- Hide the existing company_id field -->
            <field name="company_id" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            
            <!-- Add the new company_ids field -->
            <field name="company_id" position="after">
                <field name="company_ids" 
                       widget="many2many_tags" 
                       groups="base.group_multi_company" 
                       options="{'no_create': True}" 
                       readonly="state != 'draft'"/>
            </field>
            
            <!-- Update product_id domain in list view to use company_ids -->
            <xpath expr="//field[@name='line_ids']//field[@name='product_id']" position="attributes">
                <attribute name="domain">[('purchase_ok', '=', True), '|', ('company_id', '=', False), ('company_id', 'in', parent.company_ids)]</attribute>
            </xpath>
            
            <!-- Update product_id domain in form view to use company_ids -->
            <xpath expr="//field[@name='line_ids']//form//field[@name='product_id']" position="attributes">
                <attribute name="domain">[('purchase_ok', '=', True), '|', ('company_id', '=', False), ('company_id', 'in', parent.company_ids)]</attribute>
            </xpath>
        </field>
    </record>

    <!-- Inherit the purchase requisition list view to hide company_id -->
    <record id="view_purchase_requisition_tree_inherit" model="ir.ui.view">
        <field name="name">purchase.requisition.list.inherit.custom</field>
        <field name="model">purchase.requisition</field>
        <field name="inherit_id" ref="purchase_requisition.view_purchase_requisition_tree"/>
        <field name="arch" type="xml">
            <!-- Hide the existing company_id field -->
            <field name="company_id" position="attributes">
                <attribute name="invisible">1</attribute>
            </field>
            
            <!-- Add the new company_ids field -->
            <field name="company_id" position="after">
                <field name="company_ids" 
                       widget="many2many_tags" 
                       groups="base.group_multi_company" 
                       optional="show"/>
            </field>
        </field>
    </record>

</odoo>
