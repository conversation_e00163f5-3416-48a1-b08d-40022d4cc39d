# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* purchase_requisition
# 
# Translators:
# <PERSON><PERSON><PERSON>, 2024
# Halil, 2024
# <PERSON>, 2024
# <PERSON>re <PERSON>tem, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <PERSON>, 2024
# <PERSON><PERSON> Melik Sonmez, 2025
# <PERSON><PERSON>, 2025
# <PERSON><PERSON>uvener_Odoo <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 18.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-09-26 08:56+0000\n"
"PO-Revision-Date: 2024-09-25 09:41+0000\n"
"Last-Translator: Deniz Guvener_Odoo <<EMAIL>>, 2025\n"
"Language-Team: Turkish (https://app.transifex.com/odoo/teams/41243/tr/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: tr\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "$50"
msgstr "$50"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "$500"
msgstr "$500"

#. module: purchase_requisition
#: model:ir.actions.report,print_report_name:purchase_requisition.action_report_purchase_requisitions
msgid "'Purchase Agreement - %s' % (object.name)"
msgstr "'Satın Alma Sözleşmesi - %s' % (object.name)"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "02/16/2024"
msgstr "16/02/2024"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "12/25/2024"
msgstr "25/12/2024"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "2023-09-15"
msgstr "2023-09-15"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "<span><strong>From</strong></span>"
msgstr "<span><strong>Gönderen:</strong></span>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "<span><strong>to</strong></span>"
msgstr "<span><strong>alıcı</strong></span>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "<strong>Contact:</strong><br/>"
msgstr "<strong>İlgili Kişi:</strong><br/>"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "<strong>Reference:</strong><br/>"
msgstr "<strong>Referans:</strong><br/>"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_needaction
msgid "Action Needed"
msgstr "Aksiyon Gerekiyor"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__active
msgid "Active"
msgstr "Etkin"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_ids
msgid "Activities"
msgstr "Aktiviteler"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Etkinlik İstisna Dekorasyonu"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_state
msgid "Activity State"
msgstr "Aktivite Durumu"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_type_icon
msgid "Activity Type Icon"
msgstr "Aktivite Türü İmgesi"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_product_supplierinfo__purchase_requisition_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__requisition_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__name
msgid "Agreement"
msgstr "Sözleşme"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__requisition_type
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__requisition_type
msgid "Agreement Type"
msgstr "Sözleşme Türü"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Agreement Validity"
msgstr "Sözleşme Geçerlilik Süresi"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Agreement Validity:"
msgstr "Sözleşme Geçerlilik Süresi:"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__alternative_po_ids
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__alternative_po_ids
msgid "Alternative POs"
msgstr "Alternatif PO'lar"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Alternative Purchase Order"
msgstr "Alternatif Satın Alma Siparişi"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "Alternative Warning"
msgstr "Alternatif Uyarı"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Alternatives"
msgstr "alternatifler"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid "An example of a purchase agreement is a blanket order."
msgstr "Bir satın alma sözleşmesi örneği, bir çerçeve siparişidir."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__analytic_distribution
msgid "Analytic Distribution"
msgstr "Analitik Dağılımı"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__analytic_precision
msgid "Analytic Precision"
msgstr "Analitik Hassasiyet"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Archived"
msgstr "Arşivlendi"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_attachment_count
msgid "Attachment Count"
msgstr "Ek Sayısı"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "BO00004"
msgstr "BO00004"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__requisition_type__blanket_order
msgid "Blanket Order"
msgstr "Kapsamlı Sipariş"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Blanket Orders"
msgstr "Bağlantı Siparişleri"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Buyer"
msgstr "Alıcı"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_create_alternative_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Cancel"
msgstr "İptal"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "Cancel Alternatives"
msgstr "Alternatifleri İptal Et"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__cancel
msgid "Cancelled"
msgstr "İptal Edildi"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "Cancelled by the agreement associated to this quotation."
msgstr "Bu teklifle ilişkili anlaşma tarafından iptal edildi."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_uom_category_id
msgid "Category"
msgstr "Kategori"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Choose"
msgstr "Seç"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__partner_id
msgid "Choose a vendor for alternative PO"
msgstr "Alternatif PO için bir tedarikçi seçin"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Clear"
msgstr "Temizle"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Clear Selected"
msgstr "Seçileni Temizle"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Close"
msgstr "Kapat"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__done
msgid "Closed"
msgstr "Kapanmış"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Code"
msgstr "Kod"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__company_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__company_id
msgid "Company"
msgstr "Firma"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_line__company_currency_id
msgid "Company Currency"
msgstr "Şirket Para Birimi"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_line__price_total_cc
msgid "Company Subtotal"
msgstr "Şirket Ara Toplamı"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Company Total"
msgstr "Şirket Toplamı"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Compare Order Lines"
msgstr "Sipariş Satırlarını Karşılaştırın"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Compare Product Lines"
msgstr "Ürün Gruplarını Karşılaştırın"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_res_config_settings
msgid "Config Settings"
msgstr "Yapılandırma Ayarları"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Confirm"
msgstr "Onayla"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__confirmed
msgid "Confirmed"
msgstr "Doğrulanmış"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_line__product_uom_category_id
msgid ""
"Conversion between Units of Measure can only occur if they belong to the "
"same category. The conversion will be made based on the ratios."
msgstr ""
"Ölçü Birimleri arası dönüştürme yalnızca aynı kategoriye sahiplerse "
"yapılabilir. Dönüşümler oranlara göre yapılacaktır."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__copy_products
msgid "Copy Products"
msgstr "Ürünleri Kopyala"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_create_alternative_form
msgid "Create Alternative"
msgstr "Alternatif Oluştur"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid ""
"Create a call for tender by adding alternative requests for quotation to different vendors.\n"
"                            Make your choice by selecting the best combination of lead time, OTD and/or total amount.\n"
"                            By comparing product lines you can also decide to order some products from one vendor and others from another vendor."
msgstr ""
"Farklı tedarikçilere alternatif fiyatlandırma talepleri ekleyerek bir ihale çağrısı oluşturun.\n"
"                            Teslim süresi, zamanında teslimat ve/veya toplam tutarın en iyi kombinasyonunu belirleyerek seçiminizi yapın.\n"
"                            Ürün gruplarını karşılaştırarak bazı ürünleri bir satıcıdan, diğerlerini başka bir satıcıdan sipariş etmeyi de tercih edebilirsiniz."

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Create alternative"
msgstr "Alternatif oluştur"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__create_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__create_uid
msgid "Created by"
msgstr "Tarafından oluşturuldu"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__create_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__create_date
msgid "Created on"
msgstr "Oluşturuldu"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__creation_blocked
msgid "Creation Blocked"
msgstr "Oluşturma Engellendi"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__currency_id
msgid "Currency"
msgstr "Kur"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Date"
msgstr "Tarih"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Demo Reference"
msgstr "Demo Referansı"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__description
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_description_variants
msgid "Description"
msgstr "Açıklama"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "Discard"
msgstr "Sil"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__display_name
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__display_name
msgid "Display Name"
msgstr "İsim Göster"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__distribution_analytic_account_ids
msgid "Distribution Analytic Account"
msgstr "Analitik Hesap Dağılımı"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Done"
msgstr "Yapıldı"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__state__draft
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Draft"
msgstr "Taslak"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__date_end
msgid "End Date"
msgstr "Bitiş Tarihi"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"End date cannot be earlier than start date. Please check dates for "
"agreements: %s"
msgstr ""
"Bitiş tarihi, başlangıç tarihinden önce olamaz. Lütfen anlaşmalar için "
"tarihleri kontrol edin:%s"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Expected on"
msgstr "Beklendiği tarih:"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_follower_ids
msgid "Followers"
msgstr "Takipçiler"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_partner_ids
msgid "Followers (Partners)"
msgstr "Takipçiler (İş ortakları)"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Yazı tipi harika simgesi ör. fa-görevler"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid ""
"For a blanket order, you can record an agreement for a specific period\n"
"            (e.g. a year) and you order products within this agreement to benefit\n"
"            from the negotiated prices."
msgstr ""
"Bir çerçeve sipariş için, belirli bir süre (örneğin bir yıl) için \n"
"bir sözleşme kaydedebilir ve bu sözleşme kapsamındaki ürünleri \n"
"pazarlıklı fiyatlardan yararlanmak için sipariş edebilirsiniz."

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Future Activities"
msgstr "Sonraki Aktiviteler"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Group By"
msgstr "Grupla"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__has_alternatives
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_kpis_tree_inherit_purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_tree_inherit_purchase_requisition
msgid "Has Alternatives"
msgstr "Alternatifleri Var"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__has_message
msgid "Has Message"
msgstr "Mesaj Var"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__id
msgid "ID"
msgstr "ID"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_exception_icon
msgid "Icon"
msgstr "Simge"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "İstisnai bir etkinliği belirtmek için kullanılan simge."

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_needaction
msgid "If checked, new messages require your attention."
msgstr "İşaretliyse, yeni mesajlar dikkatinize sunulacak."

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_error
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "İşaretliyse, bazı mesajlar gönderi hatası içermektedir."

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__creation_blocked
msgid ""
"If the chosen vendor or if any of the products in the original PO have a "
"blocking warning then we prevent creation of alternative PO. This is because"
" normally these fields are cleared w/warning message within form view, but "
"we cannot recreate that in this case."
msgstr ""
"Seçilen tedarikçinin veya orijinal PO'daki ürünlerden herhangi birinin "
"engelleme uyarısı varsa, alternatif PO oluşturulmasını engelleriz. Bunun "
"nedeni, normalde bu alanların form görünümünde uyarı mesajıyla silinmesidir,"
" ancak bu durumda bunu yeniden oluşturamayız."

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__copy_products
msgid ""
"If this is checked, the product quantities of the original PO will be copied"
msgstr "Bu işaretlenirse, orijinal PO'daki ürün miktarları kopyalanacaktır."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_is_follower
msgid "Is Follower"
msgstr "Takipçi mi"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
msgid "Keep Alternatives"
msgstr "Alternatifleri Tut"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__write_uid
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__write_uid
msgid "Last Updated by"
msgstr "Son Güncelleyen"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__write_date
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__write_date
msgid "Last Updated on"
msgstr "Son Güncelleme"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Late Activities"
msgstr "Geciken Aktiviteler"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.res_config_settings_view_form_purchase_requisition
msgid "Link RFQs together and compare them"
msgstr "Fiyatlandırma taleplerini birbirine bağlayın ve karşılaştırın"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Link to Existing RfQ"
msgstr "Mevcut Teklif Talebi'ya Bağlantı"

#. module: purchase_requisition
#: model:res.groups,name:purchase_requisition.group_purchase_alternatives
msgid "Manage Purchase Alternatives"
msgstr "Satın Alma Alternatiflerini Yönetin"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_error
msgid "Message Delivery error"
msgstr "Mesaj Teslim hatası"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_ids
msgid "Messages"
msgstr "Mesajlar"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Mitchell Admin"
msgstr "Mitchell Admin"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Aktivite Son Tarihim"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "My Agreements"
msgstr "Sözleşmelerim"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
msgid "Name, TIN, Email, or Reference"
msgstr "Adı, VKN, E-posta veya Referans"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "New"
msgstr "Yeni"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "New Agreements"
msgstr "Yeni Sözleşmeler"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "New Quotation"
msgstr "Yeni Teklif"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Sonraki Aktivite Takvimi Etkinliği"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Sonraki Aktivite Son Tarihi"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_summary
msgid "Next Activity Summary"
msgstr "Sonraki Aktivite Özeti"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_type_id
msgid "Next Activity Type"
msgstr "Sonraki Aktivitie Türü"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Nothing to clear"
msgstr "Temizlenecek bir şey yok"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_needaction_counter
msgid "Number of Actions"
msgstr "Aksiyon Sayısı"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__order_count
msgid "Number of Orders"
msgstr "Sipariş Sayısı"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_error_counter
msgid "Number of errors"
msgstr "Hata adedi"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "İşlem gerektiren mesaj sayısı"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Teslimat hatası olan mesaj adedi"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order_group__order_ids
msgid "Order"
msgstr "Sipariş"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__qty_ordered
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Ordered"
msgstr "Sipariş edildi"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Ordering Date"
msgstr "Sipariş Tarihi"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Orders"
msgstr "Siparişler"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__origin_po_id
msgid "Origin Po"
msgstr "Kaynak PO"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_order__alternative_po_ids
msgid "Other potential purchase orders for purchasing products"
msgstr "Ürün satın almak için diğer potansiyel satın alma siparişleri"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_alternative_warning__po_ids
msgid "POs to Confirm"
msgstr "Onaylanacak PO'lar"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__product_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Product"
msgstr "Ürün"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_uom_id
msgid "Product Unit of Measure"
msgstr "Ürün Ölçü Birimi"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_product
msgid "Product Variant"
msgstr "Ürün Varyantı"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Products"
msgstr "Ürünler"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__line_ids
msgid "Products to Purchase"
msgstr "Satınalınacak Ürünler"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__requisition_id
msgid "Purchase Agreement"
msgstr "Satınalma Sözleşmeleri"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Purchase Agreement:"
msgstr "Satın Alma Sözleşmesi:"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition
#: model:ir.actions.report,name:purchase_requisition.action_report_purchase_requisitions
#: model:ir.ui.menu,name:purchase_requisition.menu_purchase_requisition_pro_mgt
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_tree
msgid "Purchase Agreements"
msgstr "Satınalma Sözleşmeleri"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_res_config_settings__group_purchase_alternatives
msgid "Purchase Alternatives"
msgstr "Satın Alma Alternatifleri"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_order__purchase_group_id
msgid "Purchase Group"
msgstr "Satın Alma Grubu"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order
msgid "Purchase Order"
msgstr "Satınalma Siparişi"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order_line
msgid "Purchase Order Line"
msgstr "Satınalma Sipariş Satırı"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Purchase Order Lines"
msgstr "Satınalma Sipariş Satırları"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__purchase_ids
msgid "Purchase Orders"
msgstr "Satınalma Siparişleri"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Purchase Orders with requisition"
msgstr "İstenmiş Satınalma Siparişleri"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Purchase Reference"
msgstr "Satın Alma Referansı"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__user_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Purchase Representative"
msgstr "Satınalma Temsilcisi"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition
msgid "Purchase Requisition"
msgstr "Satınalma İsteği"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_line
#: model:ir.model.fields,field_description:purchase_requisition.field_product_supplierinfo__purchase_requisition_line_id
msgid "Purchase Requisition Line"
msgstr "Satınalma Talep Satırı"

#. module: purchase_requisition
#: model:ir.model.fields.selection,name:purchase_requisition.selection__purchase_requisition__requisition_type__purchase_template
msgid "Purchase Template"
msgstr "Satın Alma Şablonu"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Purchase Templates"
msgstr "Satın Alma Şablonları"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__product_qty
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Quantity"
msgstr "Miktar"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "RFQ"
msgstr "Teklif Talebi"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "RFQs/Orders"
msgstr "Teklif ve Siparişler"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__rating_ids
msgid "Ratings"
msgstr "Değerlendirmeler"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__reference
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_form_inherit
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_requisition_alternative_warning_form
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Reference"
msgstr "Referans"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition_to_so
msgid "Request for Quotation"
msgstr "Teklif Talebi"

#. module: purchase_requisition
#: model:ir.actions.act_window,name:purchase_requisition.action_purchase_requisition_list
msgid "Request for Quotations"
msgstr "Teklif Talebi"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_search_inherit
msgid "Requisition"
msgstr "İstek"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Reset to Draft"
msgstr "Taslağa Ayarla"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__activity_user_id
msgid "Responsible User"
msgstr "Sorumlu Kullanıcı"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__message_has_sms_error
msgid "SMS Delivery error"
msgstr "SMS İleti hatası"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Search Purchase Agreements"
msgstr "Satınalma Sözleşmelerinde Ara"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Show all records which has next action date is before today"
msgstr "Bir sonraki eylem tarihi bugünden önce olan tüm kayıtları göster"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "Some not cleared"
msgstr "Bazıları temizlenmemiş"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid ""
"Some quantities were not cleared because their status is not a RFQ status."
msgstr ""
"Durumları bir Teklif Talebi durumu olmadığı için bazı miktarlar "
"temizlenmedi."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__date_start
msgid "Start Date"
msgstr "Başlama Tarihi"

#. module: purchase_requisition
#: model_terms:ir.actions.act_window,help:purchase_requisition.action_purchase_requisition
msgid "Start a new purchase agreement"
msgstr "Yeni bir satın alma sözleşmesi başlatın"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__state
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Status"
msgstr "Durumu"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Aktivitelere göre durum \n"
"Gecikmiş\\: Son tarih geçmiş\n"
"Bugün\\: Aktivite tarihi bugün\n"
"Planlanmış\\: Gelecek Aktiviteler."

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__supplier_info_ids
msgid "Supplier Info"
msgstr "Tedarikçi Bilgisi"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_product_supplierinfo
msgid "Supplier Pricelist"
msgstr "Tedarikçi Fiyat Listesi"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_order_group
msgid "Technical model to group PO for call to tenders"
msgstr ""
"İhale çağrıları için satınalma siparişini gruplandırmak için teknik model"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "Terms and Conditions"
msgstr "Şartlar ve Koşullar"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition_create_alternative__origin_po_id
msgid "The original PO that this alternative PO is being created for."
msgstr "Bu alternatif PO'nun oluşturulduğu orijinal PO."

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid ""
"The vendor you have selected or at least one of the products you are copying"
" from the original order has a blocking warning on it and cannot be selected"
" to create an alternative."
msgstr ""
"Seçtiğiniz bayi veya orijinal siparişten kopyaladığınız ürünlerden en az "
"birinin üzerinde engelleme uyarısı vardır ve alternatif oluşturmak için "
"seçilemez."

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "There are no quantities to clear."
msgstr "Temizlenecek miktar yok."

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"There is already an open blanket order for this supplier. We suggest you "
"complete this open blanket order, instead of creating a new one."
msgstr ""
"Bu tedarikçi için zaten açık bir kapsamlı siparişi var. Yeni bir tane "
"oluşturmak yerine bu açık kapsamlı siparişi tamamlamanızı öneririz."

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid "This is a blocking warning!\n"
msgstr "Bu bir engelleme uyarısıdır!\n"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"To close this purchase requisition, cancel related Requests for Quotation.\n"
"\n"
"Imagine the mess if someone confirms these duplicates: double the order, double the trouble :)"
msgstr ""
"Bu satın alma talebini kapatmak için ilgili Fiyatlandırma Taleplerini iptal edin.\n"
"\n"
"Birisi bu mükerrer talepleri onaylarsa ortaya çıkacak karmaşayı hayal edin: iki kat sipariş, iki kat sorun :)"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_filter
msgid "Today Activities"
msgstr "Bugünkü Aktiviteler"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Total"
msgstr "Toplam"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Kayıtlardaki istisna etkinliğinin türü."

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Unit"
msgstr "Birim"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_line__price_unit
#: model_terms:ir.ui.view,arch_db:purchase_requisition.report_purchaserequisition_document
msgid "Unit Price"
msgstr "Birim Fiyat"

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "UoM"
msgstr "Ölçü Birimi"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__vendor_id
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__partner_id
#: model_terms:ir.ui.view,arch_db:purchase_requisition.purchase_order_line_compare_tree
msgid "Vendor"
msgstr "Tedarikçi"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition_create_alternative__purchase_warn_msg
msgid "Warning Messages"
msgstr "Uyarı Mesajları"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid ""
"Warning for %(partner)s:\n"
"%(warning_message)s\n"
msgstr ""
"%(partner)s için uyarı:\n"
"%(warning_message)s\n"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/wizard/purchase_requisition_create_alternative.py:0
msgid ""
"Warning for %(product)s:\n"
"%(warning_message)s\n"
msgstr ""
"%(product)siçin uyarı:\n"
"%(warning_message)s\n"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "Warning for %s"
msgstr "%s için uyarı"

#. module: purchase_requisition
#: model:ir.model.fields,field_description:purchase_requisition.field_purchase_requisition__website_message_ids
msgid "Website Messages"
msgstr "Websitesi Mesajları"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_requisition__website_message_ids
msgid "Website communication history"
msgstr "Websitesi iletişim geçmişi"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase.py:0
msgid "What about the alternative Requests for Quotations?"
msgstr "Peki ya alternatif Fiyatlandırma Talepleri?"

#. module: purchase_requisition
#: model:ir.model.fields,help:purchase_requisition.field_purchase_order__has_alternatives
msgid ""
"Whether or not this purchase order is linked to another purchase order as an"
" alternative."
msgstr ""
"Bu satınalma siparişinin alternatif olarak başka bir satınalma siparişine "
"bağlı olup olmadığı."

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_alternative_warning
msgid "Wizard in case PO still has open alternative requests for quotation"
msgstr ""
"PO'nun hala teklif için açık alternatif talepleri olması durumunda sihirbaz"

#. module: purchase_requisition
#: model:ir.model,name:purchase_requisition.model_purchase_requisition_create_alternative
msgid "Wizard to preset values for alternative PO"
msgstr "Alternatif PO için değerleri önceden ayarlamak için sihirbaz"

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "You can only delete draft or cancelled requisitions."
msgstr "Yalnızca taslak veya iptal edilmiş talepleri silebilirsiniz."

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"You cannot change the Agreement Type or Company of a not draft purchase "
"agreement."
msgstr ""
"Taslak olmayan bir satın alma sözleşmesine ait Sözleşme Türü veya Şirketi "
"değiştiremezsiniz."

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "You cannot confirm a blanket order with lines missing a price."
msgstr "Fiyat içermeyen satırlara sahip bir açık siparişi onaylayamazsınız."

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid "You cannot confirm a blanket order with lines missing a quantity."
msgstr "Fiyat içermeyen satırlara sahip bir açık siparişi onaylayamazsınız."

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"You cannot confirm agreement '%(agreement)s' because it does not contain any"
" product lines."
msgstr ""
"Herhangi bir ürün grubu içermediği için '%(agreement)s' sözleşmesini "
"onaylayamazsınız."

#. module: purchase_requisition
#. odoo-python
#: code:addons/purchase_requisition/models/purchase_requisition.py:0
msgid ""
"You cannot have a negative or unit price of 0 for an already confirmed "
"blanket order."
msgstr ""
"Zaten onaylanmış bir açık sipariş için negatif veya 0 birim fiyat "
"belirleyemezsiniz."

#. module: purchase_requisition
#: model_terms:ir.ui.view,arch_db:purchase_requisition.view_purchase_requisition_form
msgid "e.g. PO0025"
msgstr "örn. PO0025"
